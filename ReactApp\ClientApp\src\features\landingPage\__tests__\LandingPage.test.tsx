import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import LandingPage from "../LandingPage";
import { useLandingPageController as originalController } from "../hooks/useLandingPageController";
import type { RecentActivity } from "@/types/recentActivity";

// Mock the controller hook
vi.mock("../hooks/useLandingPageController");

// Mock the AlertBox component
vi.mock("@/components/AlertBox/AlertBox", () => ({
  __esModule: true,
  default: ({ message, onClose }: { message: string; onClose: () => void }) => (
    <div data-testid="alert-box">
      <span>{message}</span>
      <button onClick={onClose} data-testid="close-alert">
        Close
      </button>
    </div>
  ),
}));

// Mock the SectionLayout component
vi.mock("@/components/dashboardLayout/SectionLayout/SectionLayout", () => ({
  __esModule: true,
  default: ({
    children,
    isLoading,
    isFetching,
  }: {
    children: React.ReactNode;
    isLoading?: boolean;
    isFetching?: boolean;
  }) => (
    <div data-testid="section-layout" data-loading={isLoading} data-fetching={isFetching}>
      {children}
    </div>
  ),
}));

// Mock the LandingRecentActivityTable component
vi.mock("../components/RecentActivityTable", () => ({
  LandingRecentActivityTable: ({
    data,
    totalRecords,
    dataState,
    isLoading,
    isFetching,
    onRefresh,
    onDataStateChange,
  }: any) => (
    <div data-testid="recent-activity-table">
      <span>Records: {data.length}</span>
      <span>Total: {totalRecords}</span>
      <button onClick={onRefresh} data-testid="refresh-button">
        Refresh
      </button>
      <button
        onClick={() =>
          onDataStateChange({
            dataState: { ...dataState, skip: dataState.skip + dataState.take },
          })
        }
        data-testid="next-page-button"
      >
        Next Page
      </button>
    </div>
  ),
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

const useLandingPageController = vi.mocked(originalController);

const mockRecentActivityData: RecentActivity[] = [
  {
    id: "1",
    setupArea: "DMS Settings",
    section: "Test Section",
    updatedBy: "John Doe",
    lastUpdated: "2025-01-15T10:30:00Z",
    relativeUrl: "/test-url",
  },
  {
    id: "2",
    setupArea: "Portal Settings",
    section: "Another Section",
    updatedBy: "Jane Smith",
    lastUpdated: "2025-01-14T09:15:00Z",
    relativeUrl: "/another-url",
  },
];

const defaultControllerValues = {
  showAlert: true,
  setShowAlert: vi.fn(),
  recentActivityData: mockRecentActivityData,
  totalRecords: 2,
  dataState: {
    skip: 0,
    take: 20,
    sort: [{ field: "lastUpdated", dir: "desc" }],
  },
  isLoadingRecentActivity: false,
  isFetchingRecentActivity: false,
  recentActivityError: undefined,
  handleRefreshRecentActivity: vi.fn(),
  handleDataStateChange: vi.fn(),
};

describe("LandingPage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    useLandingPageController.mockReturnValue(defaultControllerValues);
  });

  it("renders SectionLayout with correct loading props", () => {
    render(<LandingPage />);
    
    const sectionLayout = screen.getByTestId("section-layout");
    expect(sectionLayout).toHaveAttribute("data-loading", "false");
    expect(sectionLayout).toHaveAttribute("data-fetching", "false");
  });

  it("renders AlertBox when showAlert is true", () => {
    render(<LandingPage />);
    
    expect(screen.getByTestId("alert-box")).toBeInTheDocument();
    expect(screen.getByText("landing_page_alert_message")).toBeInTheDocument();
  });

  it("does not render AlertBox when showAlert is false", () => {
    useLandingPageController.mockReturnValue({
      ...defaultControllerValues,
      showAlert: false,
    });
    
    render(<LandingPage />);
    
    expect(screen.queryByTestId("alert-box")).not.toBeInTheDocument();
  });

  it("calls setShowAlert when alert is closed", () => {
    const mockSetShowAlert = vi.fn();
    useLandingPageController.mockReturnValue({
      ...defaultControllerValues,
      setShowAlert: mockSetShowAlert,
    });
    
    render(<LandingPage />);
    
    fireEvent.click(screen.getByTestId("close-alert"));
    expect(mockSetShowAlert).toHaveBeenCalledWith(false);
  });

  it("renders RecentActivityTable with correct props when data is available", () => {
    render(<LandingPage />);
    
    expect(screen.getByTestId("recent-activity-table")).toBeInTheDocument();
    expect(screen.getByText("Records: 2")).toBeInTheDocument();
    expect(screen.getByText("Total: 2")).toBeInTheDocument();
  });

  it("does not render RecentActivityTable when no data is available", () => {
    useLandingPageController.mockReturnValue({
      ...defaultControllerValues,
      recentActivityData: [],
    });
    
    render(<LandingPage />);
    
    expect(screen.queryByTestId("recent-activity-table")).not.toBeInTheDocument();
  });

  it("passes loading and fetching states to RecentActivityTable", () => {
    useLandingPageController.mockReturnValue({
      ...defaultControllerValues,
      isLoadingRecentActivity: true,
      isFetchingRecentActivity: true,
    });
    
    render(<LandingPage />);
    
    const sectionLayout = screen.getByTestId("section-layout");
    expect(sectionLayout).toHaveAttribute("data-loading", "true");
    expect(sectionLayout).toHaveAttribute("data-fetching", "true");
  });

  it("calls handleRefreshRecentActivity when refresh button is clicked", () => {
    const mockHandleRefresh = vi.fn();
    useLandingPageController.mockReturnValue({
      ...defaultControllerValues,
      handleRefreshRecentActivity: mockHandleRefresh,
    });
    
    render(<LandingPage />);
    
    fireEvent.click(screen.getByTestId("refresh-button"));
    expect(mockHandleRefresh).toHaveBeenCalledTimes(1);
  });

  it("calls handleDataStateChange when next page button is clicked", () => {
    const mockHandleDataStateChange = vi.fn();
    useLandingPageController.mockReturnValue({
      ...defaultControllerValues,
      handleDataStateChange: mockHandleDataStateChange,
    });
    
    render(<LandingPage />);
    
    fireEvent.click(screen.getByTestId("next-page-button"));
    expect(mockHandleDataStateChange).toHaveBeenCalledWith({
      dataState: {
        skip: 20,
        take: 20,
        sort: [{ field: "lastUpdated", dir: "desc" }],
      },
    });
  });
});
