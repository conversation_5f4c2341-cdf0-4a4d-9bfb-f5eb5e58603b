import {
  DateRangePicker,
  type DateRangePickerChangeEvent,
} from "@progress/kendo-react-dateinputs";
import { Button } from "@progress/kendo-react-buttons";
import { filterClearIcon } from "@progress/kendo-svg-icons";
import { useEffect, useState } from "react";
import "./DateRangeSelector.scss";

const DateRangeSelector = (props: any) => {
  const { value = null, onChange } = props;
  const [open, setOpen] = useState(false);

  const [internalValue, setInternalValue] = useState<{
    start: Date | null;
    end: Date | null;
  }>({
    start: value?.start ? new Date(value.start) : null,
    end: value?.end ? new Date(value.end) : null,
  });

  const handleChange = (event: DateRangePickerChangeEvent) => {
    const start = event.value?.start || null;
    const end = event.value?.end || null;

    setInternalValue({ start, end });

    if (start && end) {
      onChange({
        value: {
          start: start.toLocaleDateString("sv-SE"),
          end: end.toLocaleDateString("sv-SE"),
        },
        operator: "inRange",
        syntheticEvent: event.syntheticEvent,
      });
      setOpen(false);
    } else {
      setOpen(true);
    }
  };

  const handleClear = (event: any) => {
    event.preventDefault();
    setInternalValue({ start: null, end: null });
    onChange({
      value: null,
      operator: "",
      syntheticEvent: event,
    });
    setOpen(false);
  };

  useEffect(() => {
    if (value?.start || value?.end) {
      setInternalValue({
        start: value?.start ? new Date(value.start) : null,
        end: value?.end ? new Date(value.end) : null,
      });
    } else {
      setInternalValue({ start: null, end: null });
    }
  }, [value]);

  return (
    <div className="gridCellFilterContainer">
      <DateRangePicker
        format="dd/MM/yy"
        value={internalValue}
        onChange={handleChange}
        onFocus={() => setOpen(true)}
        popupSettings={{ show: open }}
        startDateInputSettings={{
          placeholder: "From",
          label: "",
        }}
        endDateInputSettings={{
          placeholder: "To",
          label: "",
        }}
      />
      <Button
        svgIcon={filterClearIcon}
        title="Clear"
        type="button"
        onClick={handleClear}
        disabled={!internalValue.start && !internalValue.end}
      />
    </div>
  );
};

export default DateRangeSelector;
