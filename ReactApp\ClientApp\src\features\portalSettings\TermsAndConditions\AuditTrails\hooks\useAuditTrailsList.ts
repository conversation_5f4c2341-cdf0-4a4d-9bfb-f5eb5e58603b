import { useGetAuditTrailsQuery } from "@/api/auditTrailsApiSlice";
import { useMemo } from "react";
import { bytesToMegabytes } from "@/utils/fileSizeConverter";
import { formatDate } from "@/utils/baseGridQueryParamsBuilder";
import type { AdvancedBaseGridParams } from "@/types/advancedBaseGrid";

interface UseAuditTrailsListProps {
  auditTrailsListParams: AdvancedBaseGridParams;
  enabled: boolean;
}

export const useAuditTrailsList = ({
  auditTrailsListParams,
  enabled,
}: UseAuditTrailsListProps) => {
  const { data, isLoading, isError, error, isFetching, refetch } =
    useGetAuditTrailsQuery(auditTrailsListParams, {
      skip: !enabled,
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
      refetchOnReconnect: true,
    });

  const auditTrailsData = useMemo(() => {
    if (!data?.records) return [];
    return data.records.map((trail) => ({
      ...trail,
      fileSize: bytesToMegabytes(trail?.fileSize as number) + " MB",
      uploadedOn: formatDate(trail.uploadedOn as string),
    }));
  }, [data]);

  const totalRecordCount = useMemo(() => data?.totalRecordCount || 0, [data]);

  return {
    auditTrailsData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    isFetching,
    refetch,
  };
};
