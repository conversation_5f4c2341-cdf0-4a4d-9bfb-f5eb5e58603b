import { Button } from "@progress/kendo-react-buttons";
import { useLazyDownloadAuditTrailQuery } from "@/api/auditTrailsApiSlice";
import type { AuditTrail } from "@/types/auditTrails";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

interface ExportCsvFileProps {
  dataItem: AuditTrail;
}

export default function ExportCsvFile({ dataItem }: ExportCsvFileProps) {
  const { t } = useTranslation("dashboard");
  const [triggerDownload] = useLazyDownloadAuditTrailQuery();
  const now = dayjs();

  const handleExportCSV = async () => {
    const blob = await triggerDownload(dataItem.acceptanceHistory).unwrap();
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `Terms & Conditions_Acceptance Report_${dataItem.id}_${now.format("YYYY-MM-DD HH:mm:ss")}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const hasAcceptanceHistory =
    typeof dataItem.acceptanceHistory === "string" &&
    dataItem.acceptanceHistory.trim() !== "";

  return (
    <td className="k-command-cell">
      <Button
        onClick={handleExportCSV}
        themeColor="primary"
        fillMode="flat"
        disabled={!hasAcceptanceHistory}
      >
        {t("export_csv")}
      </Button>
    </td>
  );
}
