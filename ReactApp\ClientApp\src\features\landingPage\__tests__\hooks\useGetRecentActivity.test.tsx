import { renderHook } from "@testing-library/react";
import { Provider } from "react-redux";
import { vi } from "vitest";
import React from "react";
import { store } from "@/store/store";
import { useGetRecentActivity } from "../../hooks/useGetRecentActivity";
import type { RecentActivityParams, RecentActivityResponse } from "@/types/recentActivity";

// Mock the API slice
vi.mock("@/api/recentActivityApiSlice", () => ({
  useGetRecentActivityQuery: vi.fn(),
  recentActivityApiSlice: {
    reducerPath: "recentActivityApi",
    reducer: vi.fn(),
    middleware: vi.fn(),
  },
}));

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>{children}</Provider>
);

wrapper.displayName = "TestWrapper";

const mockRecentActivityResponse: RecentActivityResponse = {
  records: [
    {
      id: "1",
      setupArea: "DMS Settings",
      section: "Test Section",
      updatedBy: "John Doe",
      lastUpdated: "2025-01-15T10:30:00Z",
      relativeUrl: "/test-url",
    },
    {
      id: "2",
      setupArea: "Portal Settings",
      section: "Another Section",
      updatedBy: "Jane Smith",
      lastUpdated: "2025-01-14T09:15:00Z",
      relativeUrl: "/another-url",
    },
  ],
  pageCount: 1,
  pageNumber: 1,
  pageSize: 20,
  totalRecordCount: 2,
};

describe("useGetRecentActivity", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("returns recent activity data when successful", async () => {
    const mockRefetch = vi.fn();
    const { useGetRecentActivityQuery } = await import("@/api/recentActivityApiSlice");
    const mockUseGetRecentActivityQuery = vi.mocked(useGetRecentActivityQuery);

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: mockRecentActivityResponse,
      isLoading: false,
      isFetching: false,
      error: undefined,
      refetch: mockRefetch,
    });

    const params: RecentActivityParams = {
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    };

    const { result } = renderHook(() => useGetRecentActivity(params), {
      wrapper,
    });

    expect(result.current.recentActivity).toEqual(mockRecentActivityResponse);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(false);
    expect(result.current.error).toBeUndefined();
    expect(result.current.refetchRecentActivity).toBe(mockRefetch);
  });

  it("returns loading state when data is being fetched", async () => {
    const mockRefetch = vi.fn();
    const { useGetRecentActivityQuery } = await import("@/api/recentActivityApiSlice");
    const mockUseGetRecentActivityQuery = vi.mocked(useGetRecentActivityQuery);

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      isFetching: false,
      error: undefined,
      refetch: mockRefetch,
    });

    const params: RecentActivityParams = {
      pageNumber: 1,
      pageSize: 20,
    };

    const { result } = renderHook(() => useGetRecentActivity(params), {
      wrapper,
    });

    expect(result.current.recentActivity).toBeUndefined();
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isFetching).toBe(false);
    expect(result.current.error).toBeUndefined();
    expect(result.current.refetchRecentActivity).toBe(mockRefetch);
  });

  it("returns fetching state when data is being refetched", async () => {
    const mockRefetch = vi.fn();
    const { useGetRecentActivityQuery } = await import("@/api/recentActivityApiSlice");
    const mockUseGetRecentActivityQuery = vi.mocked(useGetRecentActivityQuery);

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: mockRecentActivityResponse,
      isLoading: false,
      isFetching: true,
      error: undefined,
      refetch: mockRefetch,
    });

    const params: RecentActivityParams = {
      pageNumber: 2,
      pageSize: 50,
      sortField: "setupArea",
      sortDirection: "asc",
    };

    const { result } = renderHook(() => useGetRecentActivity(params), {
      wrapper,
    });

    expect(result.current.recentActivity).toEqual(mockRecentActivityResponse);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(true);
    expect(result.current.error).toBeUndefined();
    expect(result.current.refetchRecentActivity).toBe(mockRefetch);
  });

  it("returns error state when API call fails", async () => {
    const mockError = { status: 500, data: "Internal Server Error" };
    const mockRefetch = vi.fn();
    const { useGetRecentActivityQuery } = await import("@/api/recentActivityApiSlice");
    const mockUseGetRecentActivityQuery = vi.mocked(useGetRecentActivityQuery);

    mockUseGetRecentActivityQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      isFetching: false,
      error: mockError,
      refetch: mockRefetch,
    });

    const params: RecentActivityParams = {
      pageNumber: 1,
      pageSize: 20,
    };

    const { result } = renderHook(() => useGetRecentActivity(params), {
      wrapper,
    });

    expect(result.current.recentActivity).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(false);
    expect(result.current.error).toEqual(mockError);
    expect(result.current.refetchRecentActivity).toBe(mockRefetch);
  });

  it("calls useGetRecentActivityQuery with correct parameters and options", async () => {
    const { useGetRecentActivityQuery } = await import("@/api/recentActivityApiSlice");
    const mockUseGetRecentActivityQuery = vi.mocked(useGetRecentActivityQuery);

    const params: RecentActivityParams = {
      pageNumber: 3,
      pageSize: 100,
      sortField: "updatedBy",
      sortDirection: "asc",
    };

    renderHook(() => useGetRecentActivity(params), { wrapper });

    expect(mockUseGetRecentActivityQuery).toHaveBeenCalledWith(params, {
      refetchOnMountOrArgChange: true,
    });
  });

  it("handles empty parameters", async () => {
    const { useGetRecentActivityQuery } = await import("@/api/recentActivityApiSlice");
    const mockUseGetRecentActivityQuery = vi.mocked(useGetRecentActivityQuery);

    const params: RecentActivityParams = {};

    renderHook(() => useGetRecentActivity(params), { wrapper });

    expect(mockUseGetRecentActivityQuery).toHaveBeenCalledWith(params, {
      refetchOnMountOrArgChange: true,
    });
  });
});
