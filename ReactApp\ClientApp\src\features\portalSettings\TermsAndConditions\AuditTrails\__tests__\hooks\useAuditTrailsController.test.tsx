import { renderHook } from "@testing-library/react";
import { vi, describe, it, expect } from "vitest";
import { useAuditTrailsController } from "../../hooks/useAuditTrailsController";

// Mock the internal hooks
vi.mock("../../../../../../hooks/useAdvancedBaseGridController.ts", () => ({
  useAdvancedBaseGridController: () => ({
    skip: 0,
    take: 10,
    filters: [],
    sorts: [],
    handlePageChange: vi.fn(),
    handleFilterChange: vi.fn(),
    handleSortChange: vi.fn(),
    handleRefresh: vi.fn(),
  }),
}));

vi.mock("../../hooks/useGetAuditTrailsColumns.ts", () => ({
  useGetAuditTrailsColumns: () => ({
    auditTrailsColumns: [{ field: "action", title: "Action" }],
    isLoading: false,
  }),
}));

vi.mock("../../hooks/useAuditTrailsList.ts", () => ({
  useAuditTrailsList: () => ({
    auditTrailsData: [{ id: 1, action: "Login" }],
    totalRecordCount: 1,
    isLoading: false,
    isError: false,
    error: null,
    isFetching: false,
    refetch: vi.fn(),
  }),
}));

describe("useAuditTrailsController", () => {
  it("returns correct data and calls refetch on dependency change", () => {
    const { result, rerender } = renderHook(() => useAuditTrailsController());

    // Initial return values check
    expect(result.current.auditTrailsColumns).toEqual([
      { field: "action", title: "Action" },
    ]);
    expect(result.current.isColumnsLoading).toBe(false);
    expect(result.current.skip).toBe(0);
    expect(result.current.take).toBe(10);
    expect(result.current.filters).toEqual([]);
    expect(result.current.sorts).toEqual([]);
    expect(result.current.auditTrailsData).toEqual([
      { id: 1, action: "Login" },
    ]);
    expect(result.current.totalRecordCount).toBe(1);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isError).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.isFetching).toBe(false);
    expect(result.current.pagination).toEqual({ skip: 0, take: 10 });
    rerender();
  });
});
