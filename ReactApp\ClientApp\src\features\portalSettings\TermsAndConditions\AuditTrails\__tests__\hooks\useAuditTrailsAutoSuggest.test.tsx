import { renderHook, act } from "@testing-library/react";
import { vi } from "vitest";

vi.mock("@/api/auditTrailsApiSlice", () => ({
  useLazyGetAuditTrailsSearchOptionsQuery: vi.fn(),
}));

import { useLazyGetAuditTrailsSearchOptionsQuery } from "@/api/auditTrailsApiSlice";
import { useAuditTrailsAutoSuggest } from "../../hooks/useAuditTrailsAutoSuggest";

describe("useAuditTrailsAutoSuggest", () => {
  const mockTrigger = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useLazyGetAuditTrailsSearchOptionsQuery as any).mockReturnValue([
      mockTrigger,
      { isLoading: false },
    ]);
  });

  it("should return suggestions from API", async () => {
    const mockResponse = ["Login", "Logout"];
    mockTrigger.mockReturnValue({
      unwrap: () => Promise.resolve(mockResponse),
    });

    const { result } = renderHook(() => useAuditTrailsAutoSuggest());

    let suggestions: string[] = [];
    await act(async () => {
      suggestions = await result.current.fetchSuggestions("action", "log");
    });

    expect(mockTrigger).toHaveBeenCalledWith({ field: "action", value: "log" });
    expect(suggestions).toEqual(["Login", "Logout"]);
  });

  it("should return empty array when value is empty", async () => {
    const { result } = renderHook(() => useAuditTrailsAutoSuggest());

    let suggestions: string[] = [];
    await act(async () => {
      suggestions = await result.current.fetchSuggestions("action", "");
    });

    expect(mockTrigger).not.toHaveBeenCalled();
    expect(suggestions).toEqual([]);
  });

  it("should return empty array on API failure", async () => {
    mockTrigger.mockReturnValue({
      unwrap: () => Promise.reject("API error"),
    });

    const { result } = renderHook(() => useAuditTrailsAutoSuggest());

    let suggestions: string[] = [];
    await act(async () => {
      suggestions = await result.current.fetchSuggestions("action", "fail");
    });

    expect(mockTrigger).toHaveBeenCalledWith({
      field: "action",
      value: "fail",
    });
    expect(suggestions).toEqual([]);
  });
});
