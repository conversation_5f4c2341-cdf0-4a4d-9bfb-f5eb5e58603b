import { useState, useEffect, useMemo } from "react";
import type { DropDownListChangeEvent } from "@progress/kendo-react-dropdowns";
import type { TabStripSelectEventArguments } from "@progress/kendo-react-layout";
import {
  useGetClientsGridColumnsQuery,
  useGetClientListQuery,
  useAssignTemplateToClientMutation,
} from "@/api/clientsApiSlice";
import type { TypeGridColumn } from "@/types/column";
import type { TypeClient } from "@/types/clients";
import { useAdvancedBaseGridController } from "@/hooks/useAdvancedBaseGridController";
import logger from "@/utils/logger";

export function useClientsController() {
  // --- Assign logic ---
  const [activeTab, setActiveTab] = useState<number>(0);
  const [showAlert, setShowAlert] = useState<boolean>(true);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const handleTabSelect = (e: TabStripSelectEventArguments) => {
    setActiveTab(e.selected);
  };

  const handleTemplateChange = (e: DropDownListChangeEvent) => {
    setSelectedTemplate(e.value.id);
  };

  const handleClearSelection = (): void => {
    setSelectedTemplate(null);
  };

  const handleAlertClose = (): void => {
    setShowAlert(false);
  };

  // --- Assign template mutation ---
  const [assignTemplateToClient, { isLoading: isAssigning }] =
    useAssignTemplateToClientMutation();

  const handleFooterCancel = (): void => {};

  const handleFooterConfirm = async (
    clientRef: number,
    templateId: string,
  ): Promise<void> => {
    if (!clientRef || !templateId) {
      logger.error("Missing clientRef or templateId for template assignment");
      return;
    }

    try {
      await assignTemplateToClient({
        clientId: clientRef,
        templateId: templateId,
      }).unwrap();

      logger.info("Template assigned successfully");
    } catch (error) {
      logger.error("Failed to assign template:", { error });
    }
  };

  // --- Grid columns logic ---
  const { data: columnsData, isLoading: isColumnsLoading } =
    useGetClientsGridColumnsQuery();
  const [columns, setColumns] = useState<TypeGridColumn[]>([]);

  useEffect(() => {
    if (columnsData && !isColumnsLoading) {
      setColumns(columnsData.filter((item) => item.key !== "actions"));
    }
  }, [columnsData, isColumnsLoading]);

  // --- Clients list logic ---
  const {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAdvancedBaseGridController();

  const {
    data: clientListData,
    isLoading,
    refetch,
    isFetching,
  } = useGetClientListQuery({
    skip,
    take,
    filters,
    sorts,
  });

  const clientList: TypeClient[] = useMemo(
    () => clientListData?.records || [],
    [clientListData],
  );
  const totalRecordCount: number = useMemo(
    () => clientListData?.totalRecordCount || 0,
    [clientListData],
  );

  // --- Clients Page Logic ---
  const [showNotice, setShowNotice] = useState(true);
  const [selectedClient, setSelectedClient] = useState<TypeClient | null>(null);
  const [showTabStrip, setShowTabStrip] = useState(false);

  const handleCloseNotice = () => {
    setShowNotice(false);
  };

  const handleRowClick = (client: TypeClient) => {
    setSelectedClient(client);
    setShowTabStrip(true);
  };

  const handleCancel = () => {
    setSelectedClient(null);
    setShowTabStrip(false);
    setSelectedTemplate(null);
  };

  const handleConfirm = async () => {
    if (selectedClient && selectedTemplate) {
      await handleFooterConfirm(selectedClient.clientRef, selectedTemplate);
      setSelectedClient(null);
      setShowTabStrip(false);
      setSelectedTemplate(null);
    }
  };

  return {
    // Assign
    activeTab,
    showAlert,
    selectedTemplate,
    handleTabSelect,
    handleTemplateChange,
    handleClearSelection,
    handleAlertClose,
    handleFooterCancel,
    handleFooterConfirm,
    isAssigning,
    // Grid columns
    columns,
    isColumnsLoading,
    // Clients list
    clientList,
    totalRecordCount,
    isLoading,
    refetch,
    isFetching,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    filters,
    sorts,
    pagination: { skip, take },
    // Clients Page
    showNotice,
    selectedClient,
    showTabStrip,
    handleCloseNotice,
    handleRowClick,
    handleCancel,
    handleConfirm,
  };
}
