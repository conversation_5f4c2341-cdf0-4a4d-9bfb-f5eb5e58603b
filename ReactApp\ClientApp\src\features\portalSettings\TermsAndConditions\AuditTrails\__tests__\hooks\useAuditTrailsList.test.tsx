import { renderHook } from "@testing-library/react";
import { vi } from "vitest";

// Mock utils
vi.mock("@/utils/fileSizeConverter", () => ({
  bytesToMegabytes: vi.fn((bytes: number) => (bytes / 1024 / 1024).toFixed(2)),
}));

vi.mock("@/utils/baseGridQueryParamsBuilder", () => ({
  formatDate: vi.fn((dateStr: string) => `formatted-${dateStr}`),
}));

// Mock hook: useAdvancedBaseGridController
vi.mock("../../../../../../hooks/useAdvancedBaseGridController.ts", () => ({
  useAdvancedBaseGridController: () => ({
    skip: 0,
    take: 10,
    filters: [],
    sorts: [],
    handlePageChange: vi.fn(),
    handleFilterChange: vi.fn(),
    handleSortChange: vi.fn(),
    handleRefresh: vi.fn(),
  }),
}));

// Mock hook: useGetAuditTrailsColumns
vi.mock("../../hooks/useGetAuditTrailsColumns.ts", () => ({
  useGetAuditTrailsColumns: () => ({
    auditTrailsColumns: [{ field: "action", title: "Action" }],
    isLoading: false,
  }),
}));

// Mock query: useGetAuditTrailsQuery
vi.mock("@/api/auditTrailsApiSlice", () => ({
  useGetAuditTrailsQuery: vi.fn(),
}));

import { useGetAuditTrailsQuery } from "@/api/auditTrailsApiSlice";
import { useAuditTrailsList } from "../../hooks/useAuditTrailsList";

describe("useAuditTrailsList", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("returns formatted audit trail data when query has records", () => {
    (useGetAuditTrailsQuery as any).mockReturnValue({
      data: {
        records: [
          {
            id: 1,
            fileSize: 1048576,
            uploadedOn: "2023-01-01T00:00:00Z",
          },
        ],
        totalRecordCount: 1,
      },
      isLoading: false,
      isError: false,
      error: null,
      isFetching: false,
    });

    const { result } = renderHook(() =>
      useAuditTrailsList({
        auditTrailsListParams: {
          skip: 0,
          take: 10,
          filters: { logic: "and", filters: [] },
          sorts: [],
        },
        enabled: true,
      }),
    );

    expect(result.current.auditTrailsData).toEqual([
      {
        id: 1,
        fileSize: "1.00 MB",
        uploadedOn: "formatted-2023-01-01T00:00:00Z",
      },
    ]);

    expect(result.current.totalRecordCount).toBe(1);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFetching).toBe(false);
  });

  it("returns empty data when query has no records", () => {
    (useGetAuditTrailsQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
      error: null,
      isFetching: true,
    });

    const { result } = renderHook(() =>
      useAuditTrailsList({
        auditTrailsListParams: {
          skip: 0,
          take: 10,
          filters: { logic: "and", filters: [] },
          sorts: [],
        },
        enabled: true,
      }),
    );

    expect(result.current.auditTrailsData).toEqual([]);
    expect(result.current.totalRecordCount).toBe(0);
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isFetching).toBe(true);
  });
});
