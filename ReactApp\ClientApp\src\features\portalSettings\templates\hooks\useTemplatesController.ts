import { useTranslation } from "react-i18next";
import { useState } from "react";
import { useAdvancedBaseGridController } from "@/hooks/useAdvancedBaseGridController";
import { useTemplateGridColumns } from "./useTemplateGridColumns";
import { useTemplateList } from "./useTemplateList";
import { useManageTemplatePopup } from "./useManageTemplatePopup";

export const useTemplatesController = () => {
  const { t } = useTranslation("dashboard");

  const [selectedTemplateId, setSelectedTemplateId] = useState<
    string | undefined
  >(undefined);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const { columns, isColumnsLoading } = useTemplateGridColumns();

  const {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAdvancedBaseGridController();

  const templateListParams = { skip, take, filters, sorts };

  const {
    templateListData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useTemplateList({ templateListParams });

  const {
    isOpen,
    isEditMode,
    editingTemplate,
    editingTemplateId,
    formData,
    validation,
    isSubmitting,
    isTemplateLoading,
    openEditPopup,
    updateField,
    handleCreate,
    handleUpdate,
    handleCancel,
    popupKey,
    handleOpenPopup,
  } = useManageTemplatePopup();

  const handleTemplateCreateSuccess = () => {
    setSuccessMessage(t("template_created"));
    refetch();
  };

  const handleTemplateCreateError = () => {
    setErrorMessage(t("template_create_error"));
  };

  const handleTemplateUpdateSuccess = () => {
    setSuccessMessage(t("template_updated"));
    refetch();
  };

  const handleTemplateUpdateError = () => {
    setErrorMessage(t("template_update_error"));
  };

  return {
    // Grid-related
    selectedTemplateId,
    setSelectedTemplateId,
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    columns,
    isColumnsLoading,
    templateListData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
    pagination: { skip, take },

    // Success/Error UI
    successMessage,
    errorMessage,
    setSuccessMessage,
    setErrorMessage,
    handleTemplateCreateSuccess,
    handleTemplateCreateError,
    handleTemplateUpdateSuccess,
    handleTemplateUpdateError,

    // Popup-related
    isOpen,
    isEditMode,
    editingTemplate,
    editingTemplateId,
    formData,
    validation,
    isSubmitting,
    isTemplateLoading,
    openEditPopup,
    updateField,
    handleCreate,
    handleUpdate,
    handleCancel,
    popupKey,
    handleOpenPopup,
  };
};
