import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import { ExportCsvFile } from "../../components";

vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      if (key === "export_csv") return "Export CSV";
      return key;
    },
  }),
}));

vi.mock("@/api/auditTrailsApiSlice", () => ({
  useLazyDownloadAuditTrailQuery: () => [
    vi.fn(() => ({
      unwrap: () =>
        Promise.resolve(
          new Blob(["dummy content"], { type: "application/pdf" }),
        ),
    })),
  ],
}));

describe("ExportCsvFile", () => {
  const originalCreateObjectURL = URL.createObjectURL;
  const originalRevokeObjectURL = URL.revokeObjectURL;

  beforeAll(() => {
    URL.createObjectURL = vi.fn(() => "blob:http://dummy-url");
    URL.revokeObjectURL = vi.fn();
  });

  afterAll(() => {
    URL.createObjectURL = originalCreateObjectURL;
    URL.revokeObjectURL = originalRevokeObjectURL;
  });

  it("renders export button enabled when acceptance history is present", () => {
    const mockDataItem = {
      id: 1,
      fileName: "audit-log",
      acceptanceHistory: "some history",
    };

    render(<ExportCsvFile dataItem={mockDataItem as any} />);

    const button = screen.getByRole("button", { name: /export csv/i });
    expect(button).toBeInTheDocument();
    expect(button).not.toBeDisabled();
  });

  it("disables button when acceptance history is empty", () => {
    const mockDataItem = {
      id: 2,
      fileName: "audit-log",
      acceptanceHistory: "   ",
    };

    render(<ExportCsvFile dataItem={mockDataItem as any} />);

    const button = screen.getByRole("button", { name: /export csv/i });
    expect(button).toBeDisabled();
  });

  it("triggers file download on click", async () => {
    const mockDataItem = {
      id: 3,
      fileName: "download-me",
      acceptanceHistory: "valid",
    };

    const appendChildSpy = vi.spyOn(document.body, "appendChild");
    const removeChildSpy = vi.spyOn(document.body, "removeChild");

    render(<ExportCsvFile dataItem={mockDataItem as any} />);

    const button = screen.getByRole("button", { name: /export csv/i });
    await fireEvent.click(button);

    expect(URL.createObjectURL).toHaveBeenCalled();
    expect(appendChildSpy).toHaveBeenCalled();
    expect(removeChildSpy).toHaveBeenCalled();
    expect(URL.revokeObjectURL).toHaveBeenCalled();
  });
});
