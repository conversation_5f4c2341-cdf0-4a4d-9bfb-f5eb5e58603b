import { useCallback } from "react";
import { useLazyGetAuditTrailsSearchOptionsQuery } from "@/api/auditTrailsApiSlice";

export const useAuditTrailsAutoSuggest = () => {
  const [trigger, { isLoading }] = useLazyGetAuditTrailsSearchOptionsQuery();

  const fetchSuggestions = useCallback(
    async (field: string, value: string): Promise<string[]> => {
      if (!value.trim()) return [];
      try {
        const response = await trigger({ field, value }).unwrap();
        return response;
      } catch {
        return [];
      }
    },
    [trigger],
  );

  return {
    fetchSuggestions,
    isLoading,
  };
};
