import {
  createApi,
  type FetchBaseQueryError,
  type QueryReturnValue,
} from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";
import type { BaseGridParams, TypeGridColumn } from "@/types/column";
import httpVerbs from "@/utils/http/httpVerbs";
import appConfig from "@/config";
import { mockClientsColumns, mockClientsData } from "./mocks/clientsMockData";
import { mockActiveTemplatesData } from "./mocks/templatesMock";
import { baseGridQueryParamsBuilder } from "@/utils/baseGridQueryParamsBuilder";
import type { ClientListResponse } from "@/types/clients";
import type {
  ActiveTemplatesResponse,
  AssignTemplateToClientResponse,
} from "@/types/templates";
import logger from "@/utils/logger";

export const clientsApiSlice = createApi({
  reducerPath: "clientsApiSlice",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Clients", "Templates"],
  endpoints: (builder) => ({
    getClientsGridColumns: builder.query<TypeGridColumn[], void>({
      queryFn: async (_arg, _api, _extra, baseQuery) => {
        if (appConfig.featureFlags.api.CLIENTS_API_USE_MOCK) {
          await new Promise((resolve) => setTimeout(resolve, 500));

          return {
            data: mockClientsColumns,
          };
        }
        const result = await baseQuery({
          url: "/api/clients/columns",
          method: httpVerbs.GET,
          headers: {
            Accept: "text/plain",
          },
          //meta: OperationalServiceTypes.PortalService,
        });

        return result as QueryReturnValue<
          TypeGridColumn[],
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    getClientList: builder.query<ClientListResponse, BaseGridParams>({
      queryFn: async (
        { skip, take, filters, sorts },
        _api,
        _extra,
        baseQuery,
      ) => {
        if (appConfig.featureFlags.api.CLIENTS_API_USE_MOCK) {
          await new Promise((resolve) => setTimeout(resolve, 500));

          return {
            data: {
              records: mockClientsData,
              pageCount: 1,
              pageNumber: 10,
              pageSize: mockClientsData.length,
              totalRecordCount: mockClientsData.length,
            },
          };
        }
        const params = baseGridQueryParamsBuilder(skip, take, sorts, filters);

        const response = await baseQuery({
          url: "/api/clients",
          method: httpVerbs.GET,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: params,
          // meta: OperationalServiceTypes.PortalService,
        });

        return response as QueryReturnValue<
          ClientListResponse,
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    getActiveTemplates: builder.query<ActiveTemplatesResponse, void>({
      queryFn: async (_arg, _api, _extra, baseQuery) => {
        if (!appConfig.featureFlags.api.USE_TEMPLATE_API) {
          await new Promise((resolve) => setTimeout(resolve, 1500));

          return {
            data: mockActiveTemplatesData,
          };
        }

        const result = await baseQuery({
          url: "/api/PortalBinderTemplates/",
          method: httpVerbs.GET,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        });

        return result as QueryReturnValue<
          ActiveTemplatesResponse,
          FetchBaseQueryError,
          {}
        >;
      },
      providesTags: ["Templates"],
    }),
    assignTemplateToClient: builder.mutation<
      AssignTemplateToClientResponse,
      { clientId: number; templateId: string }
    >({
      queryFn: async ({ clientId, templateId }, _api, _extra, baseQuery) => {
        if (!appConfig.featureFlags.api.USE_ASSIGN_TEMPLATE_API) {
          await new Promise((resolve) => setTimeout(resolve, 500));

          logger.info("Assign Template Payload:", {
            clientId,
            templateId,
            endpoint: `/api/PortalBinder/${clientId}/assign`,
            body: { TemplateId: templateId },
          });

          return {
            data: {
              success: true,
              message: "Template assigned successfully (mock)",
            },
          };
        }

        const result = await baseQuery({
          url: `/api/PortalBinder/${clientId}/assign`,
          method: httpVerbs.POST,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          body: {
            TemplateId: templateId,
          },
        });

        return result as QueryReturnValue<
          AssignTemplateToClientResponse,
          FetchBaseQueryError,
          {}
        >;
      },
      invalidatesTags: ["Clients", "Templates"],
    }),
  }),
});

export const {
  useGetClientsGridColumnsQuery,
  useGetClientListQuery,
  useGetActiveTemplatesQuery,
  useAssignTemplateToClientMutation,
} = clientsApiSlice;
