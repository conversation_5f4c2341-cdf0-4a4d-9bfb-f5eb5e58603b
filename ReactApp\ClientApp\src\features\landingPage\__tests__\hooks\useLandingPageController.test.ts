import { renderHook, act } from "@testing-library/react";
import { Provider } from "react-redux";
import { vi } from "vitest";
import { store } from "@/store/store";
import { useLandingPageController } from "../../hooks/useLandingPageController";
import * as getHook from "../../hooks/useGetRecentActivity";
import type { RecentActivityResponse } from "@/types/recentActivity";

// Mock the useGetRecentActivity hook
vi.mock("../../hooks/useGetRecentActivity", () => ({
  useGetRecentActivity: vi.fn(),
}));

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>{children}</Provider>
);

wrapper.displayName = "TestWrapper";

const mockRecentActivityResponse: RecentActivityResponse = {
  records: [
    {
      id: "1",
      setupArea: "DMS Settings",
      section: "Test Section",
      updatedBy: "<PERSON> Do<PERSON>",
      lastUpdated: "2025-01-15T10:30:00Z",
      relativeUrl: "/test-url",
    },
    {
      id: "2",
      setupArea: "Portal Settings",
      section: "Another Section",
      updatedBy: "Jane Smith",
      lastUpdated: "2025-01-14T09:15:00Z",
      relativeUrl: "/another-url",
    },
  ],
  pageCount: 1,
  pageNumber: 1,
  pageSize: 20,
  totalRecordCount: 2,
};

describe("useLandingPageController", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("initializes with default state", () => {
    const mockRefetch = vi.fn();
    vi.spyOn(getHook, "useGetRecentActivity").mockReturnValue({
      recentActivity: undefined,
      isLoading: false,
      isFetching: false,
      error: undefined,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController(), {
      wrapper,
    });

    expect(result.current.showAlert).toBe(true);
    expect(result.current.dataState).toEqual({
      skip: 0,
      take: 20,
      sort: [{ field: "lastUpdated", dir: "desc" as const }],
    });
    expect(result.current.recentActivityData).toEqual([]);
    expect(result.current.totalRecords).toBe(0);
    expect(result.current.isLoadingRecentActivity).toBe(false);
    expect(result.current.isFetchingRecentActivity).toBe(false);
  });

  it("returns recent activity data when available", () => {
    const mockRefetch = vi.fn();
    vi.spyOn(getHook, "useGetRecentActivity").mockReturnValue({
      recentActivity: mockRecentActivityResponse,
      isLoading: false,
      isFetching: false,
      error: undefined,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController(), {
      wrapper,
    });

    expect(result.current.recentActivityData).toEqual(mockRecentActivityResponse.records);
    expect(result.current.totalRecords).toBe(mockRecentActivityResponse.totalRecordCount);
  });

  it("updates dataState when handleDataStateChange is called", () => {
    const mockRefetch = vi.fn();
    vi.spyOn(getHook, "useGetRecentActivity").mockReturnValue({
      recentActivity: mockRecentActivityResponse,
      isLoading: false,
      isFetching: false,
      error: undefined,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController(), {
      wrapper,
    });

    const newDataState = {
      skip: 20,
      take: 50,
      sort: [{ field: "setupArea", dir: "asc" }],
    };

    act(() => {
      result.current.handleDataStateChange({ dataState: newDataState });
    });

    expect(result.current.dataState).toEqual(newDataState);
  });

  it("calls refetchRecentActivity when handleRefreshRecentActivity is called", () => {
    const mockRefetch = vi.fn();
    vi.spyOn(getHook, "useGetRecentActivity").mockReturnValue({
      recentActivity: mockRecentActivityResponse,
      isLoading: false,
      isFetching: false,
      error: undefined,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController(), {
      wrapper,
    });

    act(() => {
      result.current.handleRefreshRecentActivity();
    });

    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it("toggles showAlert state when setShowAlert is called", () => {
    const mockRefetch = vi.fn();
    vi.spyOn(getHook, "useGetRecentActivity").mockReturnValue({
      recentActivity: mockRecentActivityResponse,
      isLoading: false,
      isFetching: false,
      error: undefined,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController(), {
      wrapper,
    });

    expect(result.current.showAlert).toBe(true);

    act(() => {
      result.current.setShowAlert(false);
    });

    expect(result.current.showAlert).toBe(false);
  });

  it("calculates currentPage correctly based on skip and take values", () => {
    const mockRefetch = vi.fn();
    vi.spyOn(getHook, "useGetRecentActivity").mockReturnValue({
      recentActivity: mockRecentActivityResponse,
      isLoading: false,
      isFetching: false,
      error: undefined,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController(), {
      wrapper,
    });

    // Initial state: skip=0, take=20 => page 1
    expect(getHook.useGetRecentActivity).toHaveBeenCalledWith({
      pageNumber: 1,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    });

    // Update to skip=20, take=20 => page 2
    act(() => {
      result.current.handleDataStateChange({
        dataState: {
          skip: 20,
          take: 20,
          sort: [{ field: "lastUpdated", dir: "desc" }],
        },
      });
    });

    // Check that useGetRecentActivity was called with updated params
    expect(getHook.useGetRecentActivity).toHaveBeenCalledWith({
      pageNumber: 2,
      pageSize: 20,
      sortField: "lastUpdated",
      sortDirection: "desc",
    });
  });

  it("handles sort direction changes correctly", () => {
    const mockRefetch = vi.fn();
    vi.spyOn(getHook, "useGetRecentActivity").mockReturnValue({
      recentActivity: mockRecentActivityResponse,
      isLoading: false,
      isFetching: false,
      error: undefined,
      refetchRecentActivity: mockRefetch,
    });

    const { result } = renderHook(() => useLandingPageController(), {
      wrapper,
    });

    // Update sort to a different field and direction
    act(() => {
      result.current.handleDataStateChange({
        dataState: {
          skip: 0,
          take: 20,
          sort: [{ field: "setupArea", dir: "asc" }],
        },
      });
    });

    // Check that useGetRecentActivity was called with updated sort params
    expect(getHook.useGetRecentActivity).toHaveBeenCalledWith({
      pageNumber: 1,
      pageSize: 20,
      sortField: "setupArea",
      sortDirection: "asc",
    });
  });
});
