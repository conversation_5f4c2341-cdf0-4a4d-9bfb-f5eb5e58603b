import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import AuditTrails from "../AuditTrailsPage";
import { Provider } from "react-redux";
import { store } from "@/store/store";
import { MemoryRouter } from "react-router-dom";

// Mock hooks
vi.mock("../hooks/useAuditTrailsController.ts", () => ({
  useAuditTrailsController: () => ({
    auditTrailsColumns: [{ field: "action", title: "Action" }],
    isColumnsLoading: false,
    skip: 0,
    take: 10,
    filters: [],
    sorts: [],
    handlePageChange: vi.fn(),
    handleFilterChange: vi.fn(),
    handleSortChange: vi.fn(),
    handleRefresh: vi.fn(),
    auditTrailsData: [{ id: 1, action: "Login" }],
    totalRecordCount: 1,
    isLoading: false,
    isError: false,
    error: null,
    isFetching: false,
    pagination: { skip: 0, take: 10 },
  }),
}));

// Mock AutoSuggest hook (though not directly used in render)
vi.mock("../hooks/useAuditTrailsAutoSuggest.ts", () => ({
  useAuditTrailsAutoSuggest: vi.fn(),
}));

// Mock components
vi.mock("@/components/dashboardLayout/SectionLayout/SectionLayout", () => ({
  default: ({ children }: any) => (
    <div data-testid="section-layout">{children}</div>
  ),
}));

vi.mock("@/components/AdvancedBaseGrid/AdvancedBaseGrid", () => ({
  default: (props: any) => (
    <div data-testid="advanced-base-grid">
      {props.columns?.map((col: any) => (
        <div key={col.field}>{col.title}</div>
      ))}
      <div data-testid="actions-column">{props.actionsColumn?.label}</div>
    </div>
  ),
}));

vi.mock("@/components/BaseGridFilterFactory/BaseGridFilterFactory", () => ({
  default: () => <div data-testid="base-grid-filter-factory" />,
}));

vi.mock("./components", () => ({
  ExportCsvFile: () => <button data-testid="export-csv">Export</button>,
}));

describe("AuditTrails component", () => {
  it("renders grid with correct columns and actions", () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <AuditTrails />
        </MemoryRouter>
      </Provider>,
    );

    expect(screen.getByTestId("section-layout")).toBeInTheDocument();
    expect(screen.getByTestId("advanced-base-grid")).toBeInTheDocument();

    // Checks that the column title renders
    expect(screen.getByText("Action")).toBeInTheDocument();

    // Checks actions column
    expect(screen.getByTestId("actions-column")).toHaveTextContent(
      "Acceptance History",
    );
  });
});
