import { renderHook } from "@testing-library/react";
import { vi } from "vitest";

vi.mock("@/api/auditTrailsApiSlice", () => ({
  useGetAuditTrailsColumnsQuery: vi.fn(),
}));

import { useGetAuditTrailsColumnsQuery } from "@/api/auditTrailsApiSlice";
import { useGetAuditTrailsColumns } from "../../hooks/useGetAuditTrailsColumns";

describe("useGetAuditTrailsColumns", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return empty columns while loading", () => {
    (useGetAuditTrailsColumnsQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
    });

    const { result } = renderHook(() => useGetAuditTrailsColumns());

    expect(result.current.auditTrailsColumns).toEqual([]);
    expect(result.current.isLoading).toBe(true);
  });

  it("should return filtered columns when loading is false and data exists", () => {
    const mockColumns = [
      { key: "user", title: "User" },
      { key: "action", title: "Action" },
      { key: "acceptanceHistory", title: "Acceptance History" }, // should be filtered out
    ];

    (useGetAuditTrailsColumnsQuery as any).mockReturnValue({
      data: mockColumns,
      isLoading: false,
    });

    const { result } = renderHook(() => useGetAuditTrailsColumns());

    expect(result.current.isLoading).toBe(false);

    // "acceptanceHistory" should be excluded
    expect(result.current.auditTrailsColumns).toEqual([
      { key: "user", title: "User" },
      { key: "action", title: "Action" },
    ]);
  });

  it("should not update columns if data is null", () => {
    (useGetAuditTrailsColumnsQuery as any).mockReturnValue({
      data: null,
      isLoading: false,
    });

    const { result } = renderHook(() => useGetAuditTrailsColumns());

    expect(result.current.auditTrailsColumns).toEqual([]);
    expect(result.current.isLoading).toBe(false);
  });
});
